/**
 * Examples of using direct class instantiation with global state management
 * 
 * This file demonstrates how to use the new class-based approach without composables,
 * using direct class instantiation and global state management.
 */

// ===== EXAMPLE 1: Basic Usage with Global State Manager =====

import { SharedStateManager } from "@/helpers/SharedStateManager.js";
import { UsersComposable } from "@/modules/users-management/composables/useUsersClass.js";
import { AuthComposable } from "@/modules/authentication/composables/useAuthClass.js";
import { AddressesComposable } from "@/modules/settings/composables/useAddressesClass.js";

// Basic usage in a Vue component
export const basicUsageExample = () => {
    // Get the global state manager (singleton)
    const sharedState = SharedStateManager.getInstance();
    
    // Create module-specific managers
    const usersManager = new UsersComposable();
    const authManager = new AuthComposable();
    const addressesManager = new AddressesComposable();
    
    // Access global state directly
    const {
        valid,
        isLoading,
        tableData,
        pagination,
        updateItem,
        deleteItem,
        router,
        t
    } = sharedState.getState();
    
    // Access module-specific functionality
    const {
        roles,
        getRoles,
        getItem: getUserItem,
        validation: userValidation
    } = usersManager.getAllUsers();
    
    const {
        userCredentials,
        login,
        validation: authValidation
    } = authManager.getAllAuth();
    
    const {
        form: addressForm,
        validation: addressValidation,
        addressCols,
        addressActions
    } = addressesManager.getAllAddresses();
    
    return {
        // Global state
        sharedState,
        valid,
        isLoading,
        tableData,
        pagination,
        updateItem,
        deleteItem,
        router,
        t,
        
        // Users functionality
        usersManager,
        roles,
        getRoles,
        getUserItem,
        userValidation,
        
        // Auth functionality
        authManager,
        userCredentials,
        login,
        authValidation,
        
        // Addresses functionality
        addressesManager,
        addressForm,
        addressValidation,
        addressCols,
        addressActions
    };
};

// ===== EXAMPLE 2: Vue Component Implementation =====

export const vueComponentExample = `
<template>
    <v-container v-if="!isLoading">
        <v-form v-model="valid" @submit.prevent="handleSubmit">
            <v-text-field
                v-model="itemData.first_name"
                :label="t('users.first_name')"
                :rules="userValidation.first_name"
            />
            <v-text-field
                v-model="itemData.last_name"
                :label="t('users.last_name')"
                :rules="userValidation.last_name"
            />
            <v-btn type="submit" :disabled="!valid">
                {{ t('save') }}
            </v-btn>
        </v-form>
    </v-container>
</template>

<script setup>
import { onMounted } from 'vue';
import { SharedStateManager } from "@/helpers/SharedStateManager.js";
import { UsersComposable } from "@/modules/users-management/composables/useUsersClass.js";

const props = defineProps({
    id: { required: true, type: String }
});

// Direct class instantiation
const sharedState = SharedStateManager.getInstance();
const usersManager = new UsersComposable();

// Access state and methods
const {
    valid,
    isLoading,
    itemData,
    updateItem,
    t
} = sharedState.getState();

const {
    getRoles,
    getItem,
    validation: userValidation
} = usersManager.getAllUsers();

// Lifecycle
onMounted(async () => {
    await getRoles(true);
    await getItem(props.id, true);
});

// Methods
const handleSubmit = async () => {
    await updateItem(itemData.value, 'users', true);
};
</script>
`;

// ===== EXAMPLE 3: Advanced State Management =====

export const advancedStateManagementExample = () => {
    const sharedState = SharedStateManager.getInstance();
    
    // Subscribe to state changes
    sharedState.subscribe((newState, oldState) => {
        console.log('State changed:', { newState, oldState });
    });
    
    // Update specific state values
    sharedState.updateState('isLoading', false);
    sharedState.updateState('valid', true);
    
    // Reset state when needed
    sharedState.resetState();
    
    // Debug state
    sharedState.logState();
    const snapshot = sharedState.getStateSnapshot();
    
    // Service management
    import UsersService from "@/services/users-service.js";
    import RolesService from "@/services/roles-service.js";
    
    sharedState.setService(UsersService);
    sharedState.setInnerService(RolesService);
    
    return {
        sharedState,
        snapshot
    };
};

// ===== EXAMPLE 4: Multiple Module Managers =====

export const multipleModulesExample = () => {
    // Single global state, multiple module managers
    const sharedState = SharedStateManager.getInstance();
    
    // Each module manager works with the same global state
    const usersManager = new UsersComposable();
    const addressesManager = new AddressesComposable();
    
    // Switch between different modules
    const switchToUsers = () => {
        const { setService } = sharedState.getState();
        import("@/services/users-service.js").then(UsersService => {
            sharedState.setService(UsersService.default);
        });
    };
    
    const switchToAddresses = () => {
        import("@/services/addresses-service.js").then(AddressesService => {
            sharedState.setService(AddressesService.default);
        });
    };
    
    return {
        sharedState,
        usersManager,
        addressesManager,
        switchToUsers,
        switchToAddresses
    };
};

// ===== EXAMPLE 5: Custom Module Manager =====

export class CustomModuleManager {
    constructor() {
        this.sharedState = SharedStateManager.getInstance();
        this.initializeCustom();
    }
    
    initializeCustom() {
        // Set custom service
        import("@/services/custom-service.js").then(CustomService => {
            this.sharedState.setService(CustomService.default);
        });
        
        // Custom properties
        this.customData = ref([]);
        this.customSettings = reactive({
            theme: 'dark',
            language: 'en'
        });
    }
    
    async loadCustomData() {
        const { service, errorHandle } = this.sharedState.getState();
        try {
            const response = await service.value.index({});
            this.customData.value = response.data.data;
        } catch (error) {
            await errorHandle(error);
        }
    }
    
    getCustomState() {
        return {
            ...this.sharedState.getState(),
            customData: this.customData,
            customSettings: this.customSettings,
            loadCustomData: this.loadCustomData.bind(this)
        };
    }
}

// ===== EXAMPLE 6: Benefits of Direct Class Usage =====

/**
 * Benefits of Direct Class Usage with Global State:
 * 
 * 1. **No Composable Overhead**: Direct access to class methods and properties
 * 2. **Global State Consistency**: Single source of truth across all components
 * 3. **Better Performance**: No function call overhead from composables
 * 4. **Explicit Dependencies**: Clear understanding of what each component uses
 * 5. **Easier Testing**: Direct class instantiation makes unit testing simpler
 * 6. **Type Safety**: Better TypeScript support with direct class usage
 * 7. **Memory Efficiency**: Singleton pattern reduces memory usage
 * 8. **State Persistence**: Global state persists across component lifecycle
 * 
 * Usage Patterns:
 * 
 * 1. **Single Component**: Use one module manager per component
 * 2. **Multiple Components**: Share the same global state across components
 * 3. **Complex Applications**: Multiple module managers with shared global state
 * 4. **Custom Modules**: Extend the pattern for custom functionality
 */

export {
    basicUsageExample,
    vueComponentExample,
    advancedStateManagementExample,
    multipleModulesExample,
    CustomModuleManager
};
