import {reactive, ref} from 'vue'
import AddressesService from "@/services/addresses-service.js";
import BaseShared from "@/helpers/BaseShared.js";
import addressTableItems from "../models/address-table-items.js";

export class AddressesComposable extends BaseShared {
    constructor() {
        super(); // Call parent constructor to inherit all shared functionality

        this.initializeAddressesSpecific();
        this.setupValidation();
        this.setupTableItems();
    }

    initializeAddressesSpecific() {
        // Set the services (inherited from BaseShared)
        this.service.value = AddressesService;
        this.innerService.value = AddressesService;

        // Addresses-specific reactive properties
        this.address = ref();

        this.form = reactive({
            name_ar: "",
            name_en: "",
            code: "",
            is_active: false
        });
    }

    setupValidation() {
        // Use inherited validationRules directly
        this.validation = {
            name_ar: [
                this.validationRules.required,
            ],
            name_en: [
                this.validationRules.required,
            ],
            code: [
                this.validationRules.required,
            ],
        };
    }

    setupTableItems() {
        // Use inherited properties and methods directly
        const {
            cols: addressCols,
            actions: addressActions
        } = addressTableItems(this.t, this.redirect.bind(this), this.showUpdateModal.bind(this), this.deleteItem.bind(this));

        this.addressCols = addressCols;
        this.addressActions = addressActions;
    }


}
