import {reactive, ref} from 'vue'
import AddressesService from "@/services/addresses-service.js";
import BaseShared from "@/helpers/BaseShared.js";
import addressTableItems from "../models/address-table-items.js";

export default class AddressesComposable extends BaseShared {
    constructor() {
        super();
        this.initializeAddressesSpecific();
        this.setupValidation();
        this.setupTableItems();
    }

    initializeAddressesSpecific() {
        // Set the services
        this.service.value = AddressesService;
        this.innerService.value = AddressesService;

        // Addresses-specific reactive properties
        this.address = ref();

        this.form = reactive({
            name_ar: "",
            name_en: "",
            code: "",
            is_active: false
        });
    }

    setupValidation() {
        this.validation = {
            name_ar: [
                this.validationRules.required,
            ],
            name_en: [
                this.validationRules.required,
            ],
            code: [
                this.validationRules.required,
            ],
        };
    }

    setupTableItems() {
        const {
            cols: addressCols,
            actions: addressActions
        } = addressTableItems(this.t, this.redirect.bind(this), this.showUpdateModal.bind(this), this.deleteItem.bind(this));

        this.addressCols = addressCols;
        this.addressActions = addressActions;
    }

    // Get addresses-specific reactive properties
    getAddressesReactiveProperties() {
        return {
            ...this.getReactiveProperties(),
            address: this.address,
            form: this.form,
            validation: this.validation,
            addressCols: this.addressCols,
            addressActions: this.addressActions
        };
    }

    // Get addresses-specific methods
    getAddressesMethods() {
        return {
            ...this.getMethods()
        };
    }

    // Get all addresses-specific properties and methods
    getAllAddresses() {
        return {
            ...this.getAddressesReactiveProperties(),
            ...this.getAddressesMethods()
        };
    }

    // Static method to create and return a composable-like function
    static createComposable() {
        const instance = new AddressesComposable();
        return () => instance.getAllAddresses();
    }
}
