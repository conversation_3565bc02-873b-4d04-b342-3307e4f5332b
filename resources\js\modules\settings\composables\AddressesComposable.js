import {reactive, ref} from 'vue'
import AddressesService from "@/services/addresses-service.js";
import { SharedStateManager } from "@/helpers/SharedStateManager.js";
import addressTableItems from "../models/address-table-items.js";

export class AddressesComposable {
    constructor() {
        // Get the global shared state manager
        this.sharedState = SharedStateManager.getInstance();

        this.initializeAddressesSpecific();
        this.setupValidation();
        this.setupTableItems();
    }

    initializeAddressesSpecific() {
        // Set the services in global state
        this.sharedState.setService(AddressesService);
        this.sharedState.setInnerService(AddressesService);

        // Addresses-specific reactive properties
        this.address = ref();

        this.form = reactive({
            name_ar: "",
            name_en: "",
            code: "",
            is_active: false
        });
    }

    setupValidation() {
        // Get validation rules from global state
        const { validationRules } = this.sharedState.getState();

        this.validation = {
            name_ar: [
                validationRules.required,
            ],
            name_en: [
                validationRules.required,
            ],
            code: [
                validationRules.required,
            ],
        };
    }

    setupTableItems() {
        const { t, redirect, showUpdateModal, deleteItem } = this.sharedState.getState();

        const {
            cols: addressCols,
            actions: addressActions
        } = addressTableItems(t, redirect, showUpdateModal, deleteItem);

        this.addressCols = addressCols;
        this.addressActions = addressActions;
    }

    // Get addresses-specific reactive properties
    getAddressesReactiveProperties() {
        const sharedState = this.sharedState.getState();
        return {
            ...sharedState,
            address: this.address,
            form: this.form,
            validation: this.validation,
            addressCols: this.addressCols,
            addressActions: this.addressActions
        };
    }

    // Get addresses-specific methods
    getAddressesMethods() {
        return {
            // All shared methods are already included in sharedState
        };
    }

    // Get all addresses-specific properties and methods
    getAllAddresses() {
        return {
            ...this.getAddressesReactiveProperties(),
            ...this.getAddressesMethods()
        };
    }

    // Static method to create and return a composable-like function
    static createComposable() {
        const instance = new AddressesComposable();
        return () => instance.getAllAddresses();
    }
}
