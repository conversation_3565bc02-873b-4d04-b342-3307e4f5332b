/**
 * Router-Safe Usage Examples
 * 
 * This file demonstrates how to safely use the inheritance-based architecture
 * when router context might not be available.
 */

import { UsersComposable } from "@/modules/users-management/composables/UsersComposable.js";
import { AuthComposable } from "@/modules/authentication/composables/AuthComposable.js";

// ===== EXAMPLE 1: Safe Vue Component Usage =====

export const safeVueComponentExample = `
<template>
    <v-container v-if="!usersManager.isLoading.value">
        <t-breadcrumbs
           :path="$route.path"
           :title="$t($route.meta.breadcrumb)"
       >
       </t-breadcrumbs>
        
        <v-form v-model="usersManager.valid" @submit.prevent="handleSubmit">
            <v-text-field
                v-model="usersManager.itemData.value.first_name"
                :label="usersManager.t('users.first_name')"
                :rules="usersManager.validation.first_name"
            />
            <v-btn type="submit" :disabled="!usersManager.valid.value">
                {{ usersManager.t('save') }}
            </v-btn>
        </v-form>
    </v-container>
</template>

<script setup>
import { onMounted } from 'vue';
import { UsersComposable } from "@/modules/users-management/composables/UsersComposable.js";

const props = defineProps({
    id: { required: true, type: String }
});

// Create instance - router will be available in Vue component context
const usersManager = new UsersComposable();

onMounted(async () => {
    await usersManager.getRoles(true);
    await usersManager.getItem(props.id, true);
});

const handleSubmit = async () => {
    // Router navigation will work if routeName is provided and router is available
    await usersManager.updateItem(usersManager.itemData.value, 'users', true);
};
</script>
`;

// ===== EXAMPLE 2: Safe Usage Outside Vue Component =====

export const safeOutsideVueExample = () => {
    // When using outside Vue component context, router might not be available
    const usersManager = new UsersComposable();
    
    // Check if router is available
    if (usersManager.router) {
        console.log('Router is available');
        // Safe to use router-dependent methods
        usersManager.redirect('users');
    } else {
        console.log('Router not available - using fallback navigation');
        // Use alternative navigation methods
        window.location.href = '/users';
    }
    
    // These methods will work regardless of router availability
    usersManager.loadData();
    usersManager.getRoles();
    
    // Update without navigation (no routeName provided)
    usersManager.updateItem({id: 1, name: 'test'}, '', true);
    
    return usersManager;
};

// ===== EXAMPLE 3: Router-Safe Custom Class =====

export class RouterSafeUsersManager extends UsersComposable {
    constructor() {
        super();
    }
    
    // Override redirect to provide fallback
    async redirect(routeName, id = null) {
        if (this.router) {
            // Use inherited router method
            await super.redirect(routeName, id);
        } else {
            // Fallback to window navigation
            let url = \`/\${routeName}\`;
            if (id) {
                url += \`/\${id}\`;
            }
            window.location.href = url;
        }
    }
    
    // Safe navigation method
    async navigateToUsers() {
        await this.redirect('users');
    }
    
    async navigateToUserEdit(id) {
        await this.redirect('users/update', id);
    }
    
    // Override updateItem to provide custom navigation
    async updateItem(data, routeName = '', showLoader = false) {
        if (!this.valid.value) {
            return false;
        }
        
        try {
            let response = await this.service.value.update(data.id, data, showLoader);
            notify(response.data.message);
            await this.loadData();
            this.updateModal.value = false;
            
            // Custom navigation logic
            if (routeName) {
                await this.redirect(routeName);
            }
        } catch (error) {
            await this.errorHandle(error);
        }
    }
}

// ===== EXAMPLE 4: Conditional Router Usage =====

export const conditionalRouterUsage = () => {
    const usersManager = new UsersComposable();
    
    // Method that works with or without router
    const performAction = async (data) => {
        try {
            // Perform the main action
            const response = await usersManager.service.value.update(data.id, data);
            notify(response.data.message);
            
            // Conditional navigation
            if (usersManager.router) {
                // Use Vue router
                await usersManager.router.push({ name: 'users' });
            } else {
                // Use browser navigation
                window.location.href = '/users';
            }
        } catch (error) {
            await usersManager.errorHandle(error);
        }
    };
    
    return {
        usersManager,
        performAction
    };
};

// ===== EXAMPLE 5: Template Usage Patterns =====

export const templateUsagePatterns = {
    // Use $route instead of usersManager.router in templates
    breadcrumbsPattern: \`
    <t-breadcrumbs
       :path="$route.path"
       :title="$t($route.meta.breadcrumb)"
    >
    </t-breadcrumbs>
    \`,
    
    // Use router-link for navigation in templates
    navigationPattern: \`
    <router-link :to="{ name: 'users'}">
        <v-btn class="colored-btn-cancel">
            {{ usersManager.t('cancel') }}
        </v-btn>
    </router-link>
    \`,
    
    // Use instance properties for form binding
    formPattern: \`
    <v-form v-model="usersManager.valid" @submit.prevent="handleSubmit">
        <v-text-field
            v-model="usersManager.itemData.value.first_name"
            :rules="usersManager.validation.first_name"
        />
    </v-form>
    \`
};

// ===== EXAMPLE 6: Error Handling Patterns =====

export const errorHandlingPatterns = () => {
    const usersManager = new UsersComposable();
    
    // The errorHandle method already handles router unavailability
    const safeErrorHandling = async () => {
        try {
            // Some operation that might fail
            await usersManager.service.value.someOperation();
        } catch (error) {
            // This will safely handle errors even without router
            await usersManager.errorHandle(error);
        }
    };
    
    return {
        usersManager,
        safeErrorHandling
    };
};

// ===== EXAMPLE 7: Best Practices =====

/**
 * Best Practices for Router-Safe Usage:
 * 
 * 1. **In Templates**: Use $route instead of instance.router
 * 2. **Navigation**: Use router-link in templates, check router availability in methods
 * 3. **Error Handling**: Use inherited errorHandle method (already router-safe)
 * 4. **Custom Classes**: Override router methods to provide fallbacks
 * 5. **Conditional Logic**: Check router availability before using router methods
 * 6. **Fallback Navigation**: Use window.location.href when router not available
 * 7. **Method Calls**: Most methods work without router, only navigation methods need it
 */

export const bestPracticesExample = () => {
    const usersManager = new UsersComposable();
    
    // ✅ Good: Check router availability
    const safeNavigation = async (routeName) => {
        if (usersManager.router) {
            await usersManager.redirect(routeName);
        } else {
            window.location.href = \`/\${routeName}\`;
        }
    };
    
    // ✅ Good: Use methods that don't require router
    const safeOperations = async () => {
        await usersManager.loadData();
        await usersManager.getRoles();
        await usersManager.getItem(1);
    };
    
    // ✅ Good: Update without navigation
    const safeUpdate = async (data) => {
        await usersManager.updateItem(data, '', true); // Empty routeName
    };
    
    // ❌ Bad: Direct router usage without checking
    const unsafeNavigation = async () => {
        // This might throw error if router is null
        // await usersManager.router.push({ name: 'users' });
    };
    
    return {
        usersManager,
        safeNavigation,
        safeOperations,
        safeUpdate
    };
};

export {
    safeVueComponentExample,
    safeOutsideVueExample,
    RouterSafeUsersManager,
    conditionalRouterUsage,
    templateUsagePatterns,
    errorHandlingPatterns,
    bestPracticesExample
};
