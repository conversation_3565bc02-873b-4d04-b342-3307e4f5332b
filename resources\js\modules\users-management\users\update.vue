<template>
    <v-container v-if="!usersManager.isLoading.value">
        <t-breadcrumbs
           :path="$route.path"
           :title="$t($route.meta.breadcrumb)"
       >
       </t-breadcrumbs>
    </v-container>
    <v-container v-if="!usersManager.isLoading.value">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t('users.edit') }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-form v-model="usersManager.valid" v-on:submit.prevent="saveUser">
            <v-row class="mt-n2">
                <v-col>
                    <v-text-field
                        v-model="usersManager.itemData.value.first_name"
                        :label="$t('users.first_name')"
                        variant="solo"
                        :rules="usersManager.validation.first_name"
                    >
                    </v-text-field>
                </v-col>
                <v-col>
                    <v-text-field
                        v-model="usersManager.itemData.value.last_name"
                        :label="$t('users.last_name')"
                        variant="solo"
                        :rules="usersManager.validation.last_name"
                    >
                    </v-text-field>
                </v-col>
            </v-row>
            <v-row class="mt-n5">
                <v-col>
                    <v-text-field
                        v-model="usersManager.itemData.value.email"
                        :label="$t('users.email')"
                        variant="solo"
                        :rules="usersManager.validation.email"
                    >
                    </v-text-field>
                </v-col>
                <v-col>
                    <v-text-field
                        v-model="usersManager.itemData.value.password"
                        :label="$t('users.password')"
                        variant="solo"
                    >
                    </v-text-field>
                </v-col>
            </v-row>
            <v-row class="mt-n5">
                <v-col>
                    <v-select
                        :label="$t('users.please_select_roles')"
                        v-model="usersManager.itemData.value.roles"
                        :items="usersManager.roles.value"
                        :item-title="($t('locale.lang') === 'ar') ? 'name_ar' : 'name'"
                        item-value="name"
                        :rules="usersManager.validation.roles"
                        variant="solo"
                        multiple
                        clearable
                    >
                        <template v-slot:selection="{ item, index }">
                            <span v-if="index < 6">{{ item.title }} &nbsp;</span>
                            <span
                                v-if="index === 6"
                                class="grey--text caption"
                            >
                                (+{{ usersManager.itemData.value.roles.length - 6 }}
                                {{ $t('roles.roles_selected') }}
                                )
                            </span>
                        </template>

                    </v-select>
                </v-col>
            </v-row>
            <router-link :to="{ name: 'users'}">
                <v-btn :class=" 'float-'+$t('right') " class="colored-btn-cancel">
                    {{ $t('cancel') }}
                </v-btn>
            </router-link>
            <v-btn :class="'float-'+$t('right') + ' colored-btn'"
                   type="submit"
            >
                <span class="px-2">{{ $t('edit') }}</span>
            </v-btn>

        </v-form>

    </v-container>
</template>

<script setup>
import {reactive, ref, onMounted} from 'vue'
import { UsersComposable } from "../composables/UsersComposable.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

// Using direct class instantiation with inheritance
const usersManager = new UsersComposable();

// All BaseShared properties and methods are inherited and available directly
// No need to destructure - use the instance directly

onMounted(() => {
    usersManager.getRoles(true);
    usersManager.getItem(props.id, true);
})

const saveUser = async () => {
    await usersManager.updateItem(usersManager.itemData.value, 'users', true);
}
</script>
