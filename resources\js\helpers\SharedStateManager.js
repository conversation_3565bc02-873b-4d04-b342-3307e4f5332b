import {useRouter} from 'vue-router';
import {useI18n} from "vue-i18n";
import {inject, ref, provide, watch, reactive} from "vue";
import {notify} from "@kyvg/vue3-notification";
import {createConfirmDialog} from "vuejs-confirm-dialog";
import ConfirmDialog from "@/shared/components/confirm-dialog.vue";
import store from "@/store/store.js";
import '../../css/crud.css';
import '../../css/forms.css';
import '../../css/modal.css';
import BaseValidations from './BaseValidations.js'
import cookie from "vue-cookies";

/**
 * SharedStateManager - Global state management for shared functionality
 * Singleton pattern ensures consistent state across the entire application
 */
export class SharedStateManager {
    static instance = null;

    constructor() {
        if (SharedStateManager.instance) {
            return SharedStateManager.instance;
        }

        this.initializeState();
        this.initializeConstants();
        this.initializeValidations();
        this.initializeMethods();

        SharedStateManager.instance = this;
    }

    static getInstance() {
        if (!SharedStateManager.instance) {
            SharedStateManager.instance = new SharedStateManager();
        }
        return SharedStateManager.instance;
    }

    initializeState() {
        const {t} = useI18n({});
        const router = useRouter();

        // Global reactive state
        this.state = reactive({
            // Core properties
            t,
            router,
            itemData: ref(),
            tableData: ref([]),
            service: ref(),
            innerService: ref(),
            detailsService: ref(),

            // Pagination and query
            pagination: ref({}),
            query: ref({
                search: '',
                page: 1,
                per_page: 1000,
            }),

            // Loading states
            isLoading: ref(true),
            detailsIsLoading: ref(true),

            // Modal states
            updateModal: ref(false),
            storeModal: ref(false),

            // Form validation
            valid: ref(false),

            // Parent-child relationships
            parentDetails: ref({}),
            parent: ref(null),
            master: ref(null),

            // Details data
            detailsTableData: ref([]),
            detailsPagination: ref({}),
            detailsQuery: ref({
                search: '',
                page: 1,
                per_page: 1000,
            }),

            // User data
            currentUser: store.state.auth.user,
            userPermissions: store.state.auth.user ? store.state.auth.user.permissions : [],
        });

        // Provide service for dependency injection
        provide('service', this.state.service);
    }

    initializeConstants() {
        this.constants = {
            unAuthenticated: 401,
            unAuthorized: 403,
        };
    }

    initializeValidations() {
        this.validations = new BaseValidations(this.state.t);
        this.state.validationRules = this.validations.getValidationRules();
    }

    initializeMethods() {
        // Bind all methods to maintain proper context
        this.errorHandle = this.errorHandle.bind(this);
        this.loadData = this.loadData.bind(this);
        this.loadParentData = this.loadParentData.bind(this);
        this.getItem = this.getItem.bind(this);
        this.storeItem = this.storeItem.bind(this);
        this.updateItem = this.updateItem.bind(this);
        this.deleteItem = this.deleteItem.bind(this);
        this.showStoreModal = this.showStoreModal.bind(this);
        this.showUpdateModal = this.showUpdateModal.bind(this);
        this.cancel = this.cancel.bind(this);
        this.redirect = this.redirect.bind(this);
        this.storeModalItem = this.storeModalItem.bind(this);
        this.updateModalItem = this.updateModalItem.bind(this);
        this.storeModalItemForParent = this.storeModalItemForParent.bind(this);
        this.updateModalItemForParent = this.updateModalItemForParent.bind(this);
        this.detailsLoadData = this.detailsLoadData.bind(this);
        this.saveItem = this.saveItem.bind(this);
    }

    // State management methods
    getState() {
        return {
            ...this.state,
            // Include methods in state for easy access
            errorHandle: this.errorHandle,
            loadData: this.loadData,
            loadParentData: this.loadParentData,
            getItem: this.getItem,
            storeItem: this.storeItem,
            updateItem: this.updateItem,
            deleteItem: this.deleteItem,
            showStoreModal: this.showStoreModal,
            showUpdateModal: this.showUpdateModal,
            cancel: this.cancel,
            redirect: this.redirect,
            storeModalItem: this.storeModalItem,
            updateModalItem: this.updateModalItem,
            storeModalItemForParent: this.storeModalItemForParent,
            updateModalItemForParent: this.updateModalItemForParent,
            detailsLoadData: this.detailsLoadData,
            saveItem: this.saveItem,
        };
    }

    updateState(key, value) {
        if (key in this.state) {
            this.state[key] = value;
        } else {
            console.warn(`State key '${key}' does not exist`);
        }
    }

    resetState() {
        this.state.itemData.value = null;
        this.state.tableData.value = [];
        this.state.pagination.value = {};
        this.state.isLoading.value = true;
        this.state.valid.value = false;
        this.state.updateModal.value = false;
        this.state.storeModal.value = false;
    }

    // Service management
    setService(service) {
        this.state.service.value = service;
    }

    setInnerService(service) {
        this.state.innerService.value = service;
    }

    setDetailsService(service) {
        this.state.detailsService.value = service;
    }

    // Error handling
    async errorHandle(error) {
        console.log('error');
        console.log(error);
        if (error.response.status === this.constants.unAuthenticated) {
            store.dispatch('auth/logout');
            await this.state.router.push({name: 'login'});
        } else if (error.response.status === this.constants.unAuthorized) {
            notify(this.state.t('unauthorized'));
        } else if (error.response.data.errors && Object.keys(error.response.data.errors).length > 0) {
            for (const [key, value] of Object.entries(error.response.data.errors)) {
                value.forEach(element => {
                    notify(element);
                });
            }
        } else {
            notify(error.response.data.message);
        }
    }

    // Data loading methods
    async loadData(query) {
        try {
            this.state.isLoading.value = true;
            if (query === undefined) {
                query = {
                    search: '',
                    page: 1,
                    per_page: 10,
                };
            }
            const {data: {data, meta}} = await this.state.service.value.index({
                parent_id: '',
                page: query.page,
                size: query.per_page,
                search: query.search,
            });
            this.state.tableData.value = data;
            this.state.pagination.value = {...this.state.pagination.value, page: query.page, total: meta.total};
            cookie.set(`${this.state.service.value.routPath}LoadData`, JSON.stringify({pagination: this.state.pagination.value, query: query}));
            this.state.isLoading.value = false;
        } catch (error) {
            this.state.isLoading.value = false;
            await this.errorHandle(error);
        }
    }

    async loadParentData(query, parent_name = '') {
        try {
            if (query === undefined) {
                query = {
                    search: '',
                    page: 0,
                    per_page: 10,
                };
            }

            const {data: {data, meta, parentData}} = await this.state.innerService.value.index({
                parent_id: (this.state.parent.value !== null) ? this.state.parent.value : '',
                parent_name: parent_name,
                page: query.page,
                size: query.per_page,
                search: query.search,
            });

            this.state.tableData.value = data;
            if (parentData) {
                this.state.parentDetails.value = parentData;
            }

            this.state.pagination.value = {...this.state.pagination.value, page: query.page, total: meta.total};
            cookie.set(`${this.state.service.value.routPath}${this.state.parent.value ? 'LoadParentData' : 'LoadData'}`,
                JSON.stringify({pagination: this.state.pagination.value, query: query}));
            this.state.isLoading.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async getItem(id, showLoader = false) {
        try {
            const response = await this.state.service.value.show(id, showLoader);
            this.state.itemData.value = response.data.data;
            this.state.isLoading.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    // Modal management
    showStoreModal() {
        this.state.storeModal.value = true;
    }

    showUpdateModal(item) {
        this.state.updateModal.value = true;
        this.state.itemData.value = item;
    }

    cancel() {
        this.state.storeModal.value = false;
        this.state.updateModal.value = false;
    }

    // CRUD operations
    async storeItem(data, routeName = '', showLoader = false) {
        if (!this.state.valid.value) {
            return false;
        }
        try {
            let response = await this.state.service.value.store(data, showLoader);
            notify(response.data.message);
            await this.loadData();
            this.state.storeModal.value = false;
            await this.state.router.push({ name: routeName });
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async updateItem(data, routeName = '', showLoader = false) {
        if (!this.state.valid.value) {
            return false;
        }
        try {
            let response = await this.state.service.value.update(data.id, data, showLoader);
            notify(response.data.message);
            await this.loadData();
            this.state.updateModal.value = false;
            await this.state.router.push({ name: routeName });
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async deleteItem(id) {
        const {reveal, onConfirm} = createConfirmDialog(ConfirmDialog, {
            question: this.state.t('are_you_sure'),
            title: this.state.t('delete'),
        });

        onConfirm(async () => {
            try {
                let response = await this.state.service.value.destroy(id);
                notify(response.data.message);
                await this.loadData();
            } catch (error) {
                await this.errorHandle(error);
            }
        });

        reveal();
    }

    async saveItem(data, routeName = '') {
        if (data.id) {
            await this.updateItem(data, routeName);
        } else {
            await this.storeItem(data, routeName);
        }
    }

    // Navigation
    async redirect(routeName, id = null) {
        if (id) {
            await this.state.router.push({name: routeName, params: {id: id}});
        } else {
            await this.state.router.push({name: routeName});
        }
    }

    // Modal operations
    async storeModalItem(data) {
        if (!this.state.valid.value) {
            return false;
        }
        try {
            let response = await this.state.service.value.store(data);
            notify(response.data.message);
            await this.loadData();
            this.state.storeModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async updateModalItem(data) {
        if (!this.state.valid.value) {
            return false;
        }
        try {
            let response = await this.state.service.value.update(data.id, data);
            notify(response.data.message);
            await this.loadData();
            this.state.updateModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    // Parent-child operations
    async storeModalItemForParent(data) {
        if (!this.state.valid.value) {
            return false;
        }
        try {
            let response = await this.state.innerService.value.store(data);
            notify(response.data.message);
            await this.loadParentData();
            this.state.storeModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async updateModalItemForParent(data) {
        if (!this.state.valid.value) {
            return false;
        }
        try {
            let response = await this.state.innerService.value.update(data.id, data);
            notify(response.data.message);
            await this.loadParentData();
            this.state.updateModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async detailsLoadData(query) {
        try {
            this.state.detailsIsLoading.value = true;
            if (query === undefined) {
                query = {
                    search: '',
                    page: 1,
                    per_page: 10,
                };
            }
            const {data: {data, meta}} = await this.state.detailsService.value.detailsIndex(this.state.master.value, {
                page: query.page,
                size: query.per_page,
                search: query.search,
            });
            this.state.detailsTableData.value = data;
            this.state.detailsPagination.value = {...this.state.detailsPagination.value, page: query.page, total: meta.total};
            this.state.detailsIsLoading.value = false;
        } catch (error) {
            this.state.detailsIsLoading.value = false;
            await this.errorHandle(error);
        }
    }

    // Utility methods
    subscribe(callback) {
        // Allow components to subscribe to state changes
        watch(this.state, callback, { deep: true });
    }

    // Debug methods
    logState() {
        console.log('Current SharedStateManager state:', this.state);
    }

    getStateSnapshot() {
        return JSON.parse(JSON.stringify(this.state));
    }
}
