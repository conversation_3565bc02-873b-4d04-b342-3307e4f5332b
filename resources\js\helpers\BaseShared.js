import {useRouter} from 'vue-router';
import {useI18n} from "vue-i18n";
import {ref, provide} from "vue";
import {notify} from "@kyvg/vue3-notification";
import {createConfirmDialog} from "vuejs-confirm-dialog";
import ConfirmDialog from "@/shared/components/confirm-dialog.vue";
import store from "@/store/store.js";
import '../../css/crud.css';
import '../../css/forms.css';
import '../../css/modal.css';
import BaseValidations from './BaseValidations.js'
import cookie from "vue-cookies";

export default class BaseShared {
    static instance = null;

    constructor() {
        // Singleton pattern for shared state
        if (BaseShared.instance && this.constructor === BaseShared) {
            return BaseShared.instance;
        }

        this.initializeReactiveProperties();
        this.initializeConstants();
        this.initializeValidations();

        if (this.constructor === BaseShared) {
            BaseShared.instance = this;
        }
    }

    static getInstance() {
        if (!BaseShared.instance) {
            BaseShared.instance = new BaseShared();
        }
        return BaseShared.instance;
    }

    initializeReactiveProperties() {
        const {t} = useI18n({});
        const router = useRouter();

        this.t = t;
        this.router = router;
        this.itemData = ref();
        this.tableData = ref([]);
        this.service = ref();
        this.innerService = ref();
        provide('service', this.service);
        this.detailsService = ref();
        this.pagination = ref({});
        this.valid = ref(false);
        this.query = ref({
            search: '',
            page: 1,
            per_page: 1000,
        });
        this.isLoading = ref(true);
        this.updateModal = ref(false);
        this.storeModal = ref(false);
        this.parentDetails = ref({});
        this.parent = ref(null);
        this.detailsTableData = ref([]);
        this.detailsPagination = ref({});
        this.detailsQuery = ref({
            search: '',
            page: 1,
            per_page: 1000,
        });
        this.detailsIsLoading = ref(true);
        this.master = ref(null);
        this.currentUser = store.state.auth.user;
        this.userPermissions = this.currentUser ? this.currentUser.permissions : [];
    }

    initializeConstants() {
        this.unAuthenticated = 401;
        this.unAuthorized = 403;
    }

    initializeValidations() {
        this.validations = new BaseValidations(this.t);
        this.validationRules = this.validations.getValidationRules();
    }

    async errorHandle(error) {
        console.log('error');
        console.log(error);
        if (error.response.status === this.unAuthenticated) {
            store.dispatch('auth/logout');
            await this.router.push({name: 'login'});
        } else if (error.response.status === this.unAuthorized) {
            notify(this.t('unauthorized'));
        } else if (error.response.data.errors && Object.keys(error.response.data.errors).length > 0) {
            for (const [, value] of Object.entries(error.response.data.errors)) {
                value.forEach(element => {
                    notify(element);
                });
            }
        } else {
            notify(error.response.data.message);
        }
    }

    async loadData(query) {
        try {
            this.isLoading.value = true;
            if (query === undefined) {
                query = {
                    search: '',
                    page: 1,
                    per_page: 10,
                };
            }
            const {data: {data, meta}} = await this.service.value.index({
                parent_id: '',
                page: query.page,
                size: query.per_page,
                search: query.search,
            });
            this.tableData.value = data;
            this.pagination.value = {...this.pagination.value, page: query.page, total: meta.total};
            cookie.set(`${this.service.value.routPath}LoadData`, JSON.stringify({pagination: this.pagination.value, query: query}));
            this.isLoading.value = false;
        } catch (error) {
            this.isLoading.value = false;
            await this.errorHandle(error);
        }
    }

    async loadParentData(query, parent_name = '') {
        try {
            if (query === undefined) {
                query = {
                    search: '',
                    page: 0,
                    per_page: 10,
                };
            }

            const {data: {data, meta, parentData}} = await this.innerService.value.index({
                parent_id: (this.parent.value !== null) ? this.parent.value : '',
                parent_name: parent_name,
                page: query.page,
                size: query.per_page,
                search: query.search,
            });

            this.tableData.value = data;
            if (parentData) {
                this.parentDetails.value = parentData;
            }

            this.pagination.value = {...this.pagination.value, page: query.page, total: meta.total};
            cookie.set(`${this.service.value.routPath}${this.parent.value ? 'LoadParentData' : 'LoadData'}`,
                JSON.stringify({pagination: this.pagination.value, query: query}));
            this.isLoading.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async getItem(id, showLoader = false) {
        try {
            const response = await this.service.value.show(id, showLoader);
            this.itemData.value = response.data.data;
            this.isLoading.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    showStoreModal() {
        this.storeModal.value = true;
    }

    showUpdateModal(item) {
        this.updateModal.value = true;
        this.itemData.value = item;
    }

    async storeItem(data, routeName = '', showLoader = false) {
        if (!this.valid.value) {
            return false;
        }
        try {
            let response = await this.service.value.store(data, showLoader);
            notify(response.data.message);
            await this.loadData();
            this.storeModal.value = false;
            await this.router.push({ name: routeName });
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async updateItem(data, routeName = '', showLoader = false) {
        if (!this.valid.value) {
            return false;
        }
        try {
            let response = await this.service.value.update(data.id, data, showLoader);
            notify(response.data.message);
            await this.loadData();
            this.updateModal.value = false;
            await this.router.push({ name: routeName });
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async deleteItem(id) {
        const {reveal, onConfirm} = createConfirmDialog(ConfirmDialog, {
            question: this.t('are_you_sure'),
            title: this.t('delete'),
        });

        onConfirm(async () => {
            try {
                let response = await this.service.value.destroy(id);
                notify(response.data.message);
                await this.loadData();
            } catch (error) {
                await this.errorHandle(error);
            }
        });

        reveal();
    }

    cancel() {
        this.storeModal.value = false;
        this.updateModal.value = false;
    }

    async redirect(routeName, id = null) {
        if (id) {
            await this.router.push({name: routeName, params: {id: id}});
        } else {
            await this.router.push({name: routeName});
        }
    }

    // Methods for modal operations
    async storeModalItem(data) {
        if (!this.valid.value) {
            return false;
        }
        try {
            let response = await this.service.value.store(data);
            notify(response.data.message);
            await this.loadData();
            this.storeModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async updateModalItem(data) {
        if (!this.valid.value) {
            return false;
        }
        try {
            let response = await this.service.value.update(data.id, data);
            notify(response.data.message);
            await this.loadData();
            this.updateModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async saveItem(data, routeName = '') {
        if (data.id) {
            await this.updateItem(data, routeName);
        } else {
            await this.storeItem(data, routeName);
        }
    }

    // Additional methods for parent-child operations
    async storeModalItemForParent(data) {
        if (!this.valid.value) {
            return false;
        }
        try {
            let response = await this.innerService.value.store(data);
            notify(response.data.message);
            await this.loadParentData();
            this.storeModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async updateModalItemForParent(data) {
        if (!this.valid.value) {
            return false;
        }
        try {
            let response = await this.innerService.value.update(data.id, data);
            notify(response.data.message);
            await this.loadParentData();
            this.updateModal.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async detailsLoadData(query) {
        try {
            this.detailsIsLoading.value = true;
            if (query === undefined) {
                query = {
                    search: '',
                    page: 1,
                    per_page: 10,
                };
            }
            const {data: {data, meta}} = await this.detailsService.value.detailsIndex(this.master.value, {
                page: query.page,
                size: query.per_page,
                search: query.search,
            });
            this.detailsTableData.value = data;
            this.detailsPagination.value = {...this.detailsPagination.value, page: query.page, total: meta.total};
            this.detailsIsLoading.value = false;
        } catch (error) {
            this.detailsIsLoading.value = false;
            await this.errorHandle(error);
        }
    }


}
