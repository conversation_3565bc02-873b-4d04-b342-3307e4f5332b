import BaseValidations from './BaseValidations.js';
import {useI18n} from "vue-i18n";

// Create a singleton instance to maintain state across components
let validationsInstance = null;

export default function useValidationsClass() {
    if (!validationsInstance) {
        const {t} = useI18n({});
        validationsInstance = new BaseValidations(t);
    }
    
    return validationsInstance.getValidationRules();
}

// Export the class for direct usage
export { BaseValidations };
