import {reactive, ref} from 'vue'
import {notify} from "@kyvg/vue3-notification";
import store from "@/store/store.js";
import { VForm } from 'vuetify/lib/components/index';
import BaseShared from '@/helpers/BaseShared.js'

export class AuthComposable extends BaseShared {
    constructor() {
        super(); // Call parent constructor to inherit all shared functionality

        this.initializeAuthSpecific();
        this.setupValidation();
    }

    initializeAuthSpecific() {
        // Auth-specific reactive properties
        this.form = ref<typeof VForm>(null);
        this.showPassword = ref(false);

        this.userCredentials = reactive({
            email: "",
            password: ""
        });
    }

    setupValidation() {
        // Use inherited validationRules directly
        this.validation = {
            email: [
                this.validationRules.required,
                this.validationRules.email
            ],
            password: [
                this.validationRules.required,
                this.validationRules.password
            ],
        };
    }

    async login(userData) {
        if (this.valid.value) {
            try {
                await store.dispatch("auth/login", userData);
                await this.router.push("/");
            } catch (error) {
                notify(error.response.data.message);
            }
        }
    }


}
