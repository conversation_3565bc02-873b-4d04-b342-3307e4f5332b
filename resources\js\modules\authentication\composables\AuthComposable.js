import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router';
import {notify} from "@kyvg/vue3-notification";
import store from "@/store/store.js";
import { VForm } from 'vuetify/lib/components/index';
import {useI18n} from "vue-i18n";
import { SharedStateManager } from '@/helpers/SharedStateManager.js'

export class AuthComposable {
    constructor() {
        // Get the global shared state manager
        this.sharedState = SharedStateManager.getInstance();

        this.initializeReactiveProperties();
        this.setupValidation();
    }

    initializeReactiveProperties() {
        const {t} = useI18n({});
        const router = useRouter();

        this.t = t;
        this.router = router;
        this.form = ref<typeof VForm>(null);
        this.valid = ref(false);
        this.showPassword = ref(false);

        this.userCredentials = reactive({
            email: "",
            password: ""
        });
    }

    setupValidation() {
        // Get validation rules from global state
        const { validationRules } = this.sharedState.getState();

        this.validation = {
            email: [
                validationRules.required,
                validationRules.email
            ],
            password: [
                validationRules.required,
                validationRules.password
            ],
        };
    }

    async login(userData) {
        if (this.valid.value) {
            try {
                await store.dispatch("auth/login", userData);
                await this.router.push("/");
            } catch (error) {
                notify(error.response.data.message);
            }
        }
    }

    // Get auth-specific reactive properties
    getAuthReactiveProperties() {
        return {
            showPassword: this.showPassword,
            validation: this.validation,
            userCredentials: this.userCredentials,
            form: this.form,
            valid: this.valid,
            t: this.t,
            router: this.router,
            validationRules: this.validationRules
        };
    }

    // Get auth-specific methods
    getAuthMethods() {
        return {
            login: this.login.bind(this)
        };
    }

    // Get all auth-specific properties and methods
    getAllAuth() {
        return {
            ...this.getAuthReactiveProperties(),
            ...this.getAuthMethods()
        };
    }

    // Static method to create and return a composable-like function
    static createComposable() {
        const instance = new AuthComposable();
        return () => instance.getAllAuth();
    }
}
