# Class-Based Architecture for Shared Utilities and Composables

This document describes the new class-based architecture implemented for shared utilities and composables, following the same pattern as the existing BaseService and its child classes.

## Overview

The codebase has been refactored to use a class-based approach for shared functionality while maintaining backward compatibility with the existing function-based composables.

## Architecture

### Base Classes

#### 1. BaseShared (`resources/js/helpers/BaseShared.js`)
- Core shared functionality for all composables
- Handles common operations like CRUD, error handling, loading states
- Provides reactive properties and methods
- Similar to BaseService but for frontend composables

#### 2. BaseValidations (`resources/js/helpers/BaseValidations.js`)
- Centralized validation rules and logic
- Extensible validation system
- Type-safe validation methods

### Module-Specific Classes

#### 1. UsersComposable (`resources/js/modules/users-management/composables/UsersComposable.js`)
- Extends BaseShared
- Adds user-specific functionality
- Handles user CRUD operations and role management

#### 2. AuthComposable (`resources/js/modules/authentication/composables/AuthComposable.js`)
- Standalone class for authentication
- Handles login/logout functionality
- Manages user credentials and validation

#### 3. AddressesComposable (`resources/js/modules/settings/composables/AddressesComposable.js`)
- Extends BaseShared
- Adds address-specific functionality
- Handles hierarchical address data

## Usage Patterns

### 1. Composable Wrapper (Recommended for Migration)

```javascript
// Use the wrapper function (maintains composable pattern)
import useSharedClass from "@/helpers/useSharedClass.js";
import useUsersClass from "@/modules/users-management/composables/useUsersClass.js";

const {
    valid,
    isLoading,
    updateItem,
    router,
} = useSharedClass();

const {
    getRoles,
    roles,
    itemData,
    getItem,
    validation,
} = useUsersClass();
```

### 2. Direct Class Usage (Advanced)

```javascript
// Direct class instantiation
import { BaseShared } from "@/helpers/useSharedClass.js";
import { UsersComposable } from "@/modules/users-management/composables/useUsersClass.js";

const sharedInstance = new BaseShared();
const usersInstance = new UsersComposable();

const sharedData = sharedInstance.getAll();
const usersData = usersInstance.getAllUsers();
```

### 3. Creating Custom Composables

```javascript
import BaseShared from "@/helpers/BaseShared.js";
import CustomService from "@/services/custom-service.js";

class CustomComposable extends BaseShared {
    constructor() {
        super();
        this.service.value = CustomService;
        this.customProperty = ref('value');
    }

    customMethod() {
        // Custom logic
    }

    getAllCustom() {
        return {
            ...this.getAll(),
            customProperty: this.customProperty,
            customMethod: this.customMethod.bind(this)
        };
    }
}
```

## Migration Guide

### Step 1: Update Imports
Replace function-based imports with class-based ones:

```javascript
// Old
import useShared from "@/helpers/shared.js";
import useUsers from "../composables/users.js";

// New
import useSharedClass from "@/helpers/useSharedClass.js";
import useUsersClass from "../composables/useUsersClass.js";
```

### Step 2: Update Destructuring
The API remains the same, so destructuring doesn't need to change:

```javascript
const {
    valid,
    isLoading,
    updateItem,
    // ... other properties
} = useSharedClass(); // or useShared() - both work
```

### Step 3: Gradual Migration
- Keep existing function-based composables for backward compatibility
- Migrate components one by one to the class-based approach
- New features should use the class-based approach

## Benefits

### 1. Inheritance
Classes can extend BaseShared and inherit all functionality:

```javascript
class CustomComposable extends BaseShared {
    // Inherits all BaseShared functionality
    // Add custom methods and properties
}
```

### 2. Encapsulation
Related data and methods are grouped together in a single class.

### 3. Reusability
Base classes can be extended for specific use cases without code duplication.

### 4. Type Safety
Better TypeScript support when migrating to TypeScript.

### 5. Method Binding
Methods are properly bound to the class instance, preventing context issues.

### 6. State Management
Singleton pattern ensures consistent state across components.

### 7. Extensibility
Easy to add new methods and properties to base classes.

### 8. Backward Compatibility
Old composable pattern still works through wrapper functions.

## File Structure

```
resources/js/
├── helpers/
│   ├── BaseShared.js              # Base class for shared functionality
│   ├── BaseValidations.js         # Base class for validations
│   ├── useSharedClass.js          # Wrapper for BaseShared
│   ├── useValidationsClass.js     # Wrapper for BaseValidations
│   ├── shared.js                  # Original function-based (kept for compatibility)
│   └── validations.js             # Original function-based (kept for compatibility)
├── modules/
│   ├── users-management/
│   │   └── composables/
│   │       ├── UsersComposable.js     # Class-based users composable
│   │       ├── useUsersClass.js       # Wrapper for UsersComposable
│   │       └── users.js               # Original function-based (kept for compatibility)
│   ├── authentication/
│   │   └── composables/
│   │       ├── AuthComposable.js      # Class-based auth composable
│   │       ├── useAuthClass.js        # Wrapper for AuthComposable
│   │       └── auth.js                # Original function-based (kept for compatibility)
│   └── settings/
│       └── composables/
│           ├── AddressesComposable.js # Class-based addresses composable
│           ├── useAddressesClass.js   # Wrapper for AddressesComposable
│           └── addresses.js           # Original function-based (kept for compatibility)
└── examples/
    └── class-based-usage-examples.js  # Usage examples and patterns
```

## Best Practices

1. **Use wrapper functions** for gradual migration
2. **Extend BaseShared** for new module-specific composables
3. **Keep method binding** when returning methods from classes
4. **Use singleton pattern** for state consistency
5. **Maintain backward compatibility** during migration
6. **Document custom classes** with clear interfaces
7. **Test thoroughly** when migrating existing components

## Future Considerations

- **TypeScript Migration**: Classes provide better TypeScript support
- **Testing**: Easier to unit test class methods
- **Performance**: Potential performance improvements with proper instantiation
- **Maintenance**: Easier to maintain and extend functionality
