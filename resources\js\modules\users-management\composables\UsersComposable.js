import {ref} from 'vue'
import UsersService from "@/services/users-service.js";
import BaseShared from "@/helpers/BaseShared.js";
import RolesService from "@/services/roles-service.js";
import userTableItems from "../models/user-table-items";
import roleTableItems from "../models/role-table-items";

export class UsersComposable extends BaseShared {
    constructor() {
        super(); // Call parent constructor to inherit all shared functionality

        this.initializeUsersSpecific();
        this.setupValidation();
    }

    initializeUsersSpecific() {
        // Set the service (inherited from BaseShared)
        this.service.value = UsersService;

        // Users-specific reactive properties
        this.roles = ref([]);
    }

    setupValidation() {
        // Use inherited validationRules directly
        this.validation = {
            first_name: [
                this.validationRules.required,
            ],
            last_name: [
                this.validationRules.required,
            ],
            email: [
                this.validationRules.required,
                this.validationRules.email
            ],
            password: [
                this.validationRules.required,
                this.validationRules.password
            ],
            roles: [
                this.validationRules.required,
            ],
        };
    }

    async getItem(id, showLoader = false) {
        try {
            // Use inherited properties directly
            const response = await this.service.value.show(id, showLoader);
            this.itemData.value = response.data.data['user'];
            this.itemData.value['roles'] = response.data.data['roles'];
            this.isLoading.value = false;
        } catch (error) {
            // Use inherited errorHandle method
            await this.errorHandle(error);
        }
    }

    async getRoles(showLoader = false) {
        try {
            const response = await RolesService.index({
                parent_id: '',
                page: 1,
                size: 1000,
                search: '',
            }, showLoader);
            this.roles.value = response.data.data;
        } catch (error) {
            // Use inherited errorHandle method
            await this.errorHandle(error);
        }
    }


}
