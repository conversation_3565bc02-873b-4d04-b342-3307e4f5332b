import {ref} from 'vue'
import UsersService from "@/services/users-service.js";
import { SharedStateManager } from "@/helpers/SharedStateManager.js";
import RolesService from "@/services/roles-service.js";
import userTableItems from "../models/user-table-items";
import roleTableItems from "../models/role-table-items";

export class UsersComposable {
    constructor() {
        // Get the global shared state manager
        this.sharedState = SharedStateManager.getInstance();

        this.initializeUsersSpecific();
        this.setupValidation();
    }

    initializeUsersSpecific() {
        // Set the service in global state
        this.sharedState.setService(UsersService);

        // Users-specific reactive properties
        this.roles = ref([]);
    }

    setupValidation() {
        // Get validation rules from global state
        const { validationRules } = this.sharedState.getState();

        this.validation = {
            first_name: [
                validationRules.required,
            ],
            last_name: [
                validationRules.required,
            ],
            email: [
                validationRules.required,
                validationRules.email
            ],
            password: [
                validationRules.required,
                validationRules.password
            ],
            roles: [
                validationRules.required,
            ],
        };
    }

    async getItem(id, showLoader = false) {
        try {
            const { service, itemData, isLoading } = this.sharedState.getState();
            const response = await service.value.show(id, showLoader);
            itemData.value = response.data.data['user'];
            itemData.value['roles'] = response.data.data['roles'];
            isLoading.value = false;
        } catch (error) {
            await this.sharedState.errorHandle(error);
        }
    }

    async getRoles(showLoader = false) {
        try {
            const response = await RolesService.index({
                parent_id: '',
                page: 1,
                size: 1000,
                search: '',
            }, showLoader);
            this.roles.value = response.data.data;
        } catch (error) {
            await this.sharedState.errorHandle(error);
        }
    }

    // Get users-specific reactive properties
    getUsersReactiveProperties() {
        const sharedState = this.sharedState.getState();
        return {
            ...sharedState,
            roles: this.roles,
            validation: this.validation
        };
    }

    // Get users-specific methods
    getUsersMethods() {
        return {
            getRoles: this.getRoles.bind(this),
            getItem: this.getItem.bind(this) // Override the base getItem method
        };
    }

    // Get all users-specific properties and methods
    getAllUsers() {
        return {
            ...this.getUsersReactiveProperties(),
            ...this.getUsersMethods()
        };
    }

    // Static method to create and return a composable-like function
    static createComposable() {
        const instance = new UsersComposable();
        return () => instance.getAllUsers();
    }
}
