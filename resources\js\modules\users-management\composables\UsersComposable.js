import {ref} from 'vue'
import UsersService from "@/services/users-service.js";
import BaseShared from "@/helpers/BaseShared.js";
import RolesService from "@/services/roles-service.js";
import userTableItems from "../models/user-table-items";
import roleTableItems from "../models/role-table-items";

export default class UsersComposable extends BaseShared {
    constructor() {
        super();
        this.initializeUsersSpecific();
        this.setupValidation();
    }

    initializeUsersSpecific() {
        // Set the service
        this.service.value = UsersService;
        
        // Users-specific reactive properties
        this.roles = ref([]);
    }

    setupValidation() {
        this.validation = {
            first_name: [
                this.validationRules.required,
            ],
            last_name: [
                this.validationRules.required,
            ],
            email: [
                this.validationRules.required,
                this.validationRules.email
            ],
            password: [
                this.validationRules.required,
                this.validationRules.password
            ],
            roles: [
                this.validationRules.required,
            ],
        };
    }

    async getItem(id, showLoader = false) {
        try {
            const response = await this.service.value.show(id, showLoader);
            this.itemData.value = response.data.data['user'];
            this.itemData.value['roles'] = response.data.data['roles'];
            this.isLoading.value = false;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    async getRoles(showLoader = false) {
        try {
            const response = await RolesService.index({
                parent_id: '',
                page: 1,
                size: 1000,
                search: '',
            }, showLoader);
            this.roles.value = response.data.data;
        } catch (error) {
            await this.errorHandle(error);
        }
    }

    // Get users-specific reactive properties
    getUsersReactiveProperties() {
        return {
            ...this.getReactiveProperties(),
            roles: this.roles,
            validation: this.validation
        };
    }

    // Get users-specific methods
    getUsersMethods() {
        return {
            ...this.getMethods(),
            getRoles: this.getRoles.bind(this),
            getItem: this.getItem.bind(this) // Override the base getItem method
        };
    }

    // Get all users-specific properties and methods
    getAllUsers() {
        return {
            ...this.getUsersReactiveProperties(),
            ...this.getUsersMethods()
        };
    }

    // Static method to create and return a composable-like function
    static createComposable() {
        const instance = new UsersComposable();
        return () => instance.getAllUsers();
    }
}
