/**
 * Examples of using direct class inheritance approach
 * 
 * This file demonstrates how to use the inheritance-based architecture where
 * module classes inherit from BaseShared, giving them direct access to all
 * shared variables and functions.
 */

// ===== EXAMPLE 1: Basic Inheritance Usage =====

import BaseShared from "@/helpers/BaseShared.js";
import { UsersComposable } from "@/modules/users-management/composables/UsersComposable.js";
import { AuthComposable } from "@/modules/authentication/composables/AuthComposable.js";
import { AddressesComposable } from "@/modules/settings/composables/AddressesComposable.js";

// Basic usage in a Vue component
export const basicInheritanceExample = () => {
    // Create instances - each inherits all BaseShared functionality
    const usersManager = new UsersComposable();
    const authManager = new AuthComposable();
    const addressesManager = new AddressesComposable();
    
    // All BaseShared properties and methods are directly available
    console.log('Users Manager has access to:');
    console.log('- isLoading:', usersManager.isLoading);
    console.log('- valid:', usersManager.valid);
    console.log('- tableData:', usersManager.tableData);
    console.log('- service:', usersManager.service);
    console.log('- validationRules:', usersManager.validationRules);
    
    // Plus users-specific properties
    console.log('- roles:', usersManager.roles);
    console.log('- validation:', usersManager.validation);
    
    // All BaseShared methods are directly available
    usersManager.loadData();
    usersManager.getItem(1);
    usersManager.updateItem({id: 1, name: 'test'});
    usersManager.deleteItem(1);
    
    // Plus users-specific methods
    usersManager.getRoles();
    
    return {
        usersManager,
        authManager,
        addressesManager
    };
};

// ===== EXAMPLE 2: Vue Component Implementation =====

export const vueComponentInheritanceExample = `
<template>
    <v-container v-if="!usersManager.isLoading.value">
        <v-form v-model="usersManager.valid" @submit.prevent="handleSubmit">
            <v-text-field
                v-model="usersManager.itemData.value.first_name"
                :label="usersManager.t('users.first_name')"
                :rules="usersManager.validation.first_name"
            />
            <v-text-field
                v-model="usersManager.itemData.value.last_name"
                :label="usersManager.t('users.last_name')"
                :rules="usersManager.validation.last_name"
            />
            <v-select
                v-model="usersManager.itemData.value.roles"
                :items="usersManager.roles.value"
                :rules="usersManager.validation.roles"
                multiple
            />
            <v-btn type="submit" :disabled="!usersManager.valid.value">
                {{ usersManager.t('save') }}
            </v-btn>
        </v-form>
    </v-container>
</template>

<script setup>
import { onMounted } from 'vue';
import { UsersComposable } from "@/modules/users-management/composables/UsersComposable.js";

const props = defineProps({
    id: { required: true, type: String }
});

// Direct class instantiation with inheritance
const usersManager = new UsersComposable();

// All BaseShared functionality is inherited and available directly
// No need for destructuring or wrapper functions

onMounted(async () => {
    // Use inherited and specific methods directly
    await usersManager.getRoles(true);
    await usersManager.getItem(props.id, true);
});

const handleSubmit = async () => {
    // Use inherited methods directly
    await usersManager.updateItem(usersManager.itemData.value, 'users', true);
};
</script>
`;

// ===== EXAMPLE 3: Creating Custom Inherited Classes =====

export class CustomModuleManager extends BaseShared {
    constructor() {
        super(); // Inherit all BaseShared functionality
        
        this.initializeCustom();
        this.setupCustomValidation();
    }
    
    initializeCustom() {
        // Set custom service (inherited service property)
        import("@/services/custom-service.js").then(CustomService => {
            this.service.value = CustomService.default;
        });
        
        // Custom properties
        this.customData = ref([]);
        this.customSettings = reactive({
            theme: 'dark',
            language: 'en'
        });
    }
    
    setupCustomValidation() {
        // Use inherited validationRules
        this.customValidation = {
            customField: [
                this.validationRules.required,
                this.validationRules.minLength(3)
            ]
        };
    }
    
    async loadCustomData() {
        try {
            // Use inherited properties and methods
            this.isLoading.value = true;
            const response = await this.service.value.index({});
            this.customData.value = response.data.data;
            this.isLoading.value = false;
        } catch (error) {
            // Use inherited error handling
            await this.errorHandle(error);
        }
    }
    
    async saveCustomItem(data) {
        // Use inherited validation and methods
        if (!this.valid.value) return false;
        
        try {
            const response = await this.service.value.store(data);
            // Use inherited notification (through errorHandle success path)
            notify(response.data.message);
            await this.loadCustomData(); // Use custom method
        } catch (error) {
            await this.errorHandle(error); // Use inherited error handling
        }
    }
}

// ===== EXAMPLE 4: Singleton Pattern with Inheritance =====

export class SingletonUsersManager extends BaseShared {
    static instance = null;
    
    constructor() {
        // Singleton pattern for specific module
        if (SingletonUsersManager.instance) {
            return SingletonUsersManager.instance;
        }
        
        super(); // Inherit all BaseShared functionality
        
        this.initializeUsers();
        SingletonUsersManager.instance = this;
    }
    
    static getInstance() {
        if (!SingletonUsersManager.instance) {
            SingletonUsersManager.instance = new SingletonUsersManager();
        }
        return SingletonUsersManager.instance;
    }
    
    initializeUsers() {
        this.service.value = UsersService;
        this.roles = ref([]);
        this.setupUsersValidation();
    }
    
    setupUsersValidation() {
        this.validation = {
            first_name: [this.validationRules.required],
            last_name: [this.validationRules.required],
            email: [this.validationRules.required, this.validationRules.email]
        };
    }
}

// Usage
const usersSingleton1 = SingletonUsersManager.getInstance();
const usersSingleton2 = SingletonUsersManager.getInstance();
console.log(usersSingleton1 === usersSingleton2); // true - same instance

// ===== EXAMPLE 5: Multiple Inheritance Levels =====

export class ExtendedUsersManager extends UsersComposable {
    constructor() {
        super(); // Inherit from UsersComposable (which inherits from BaseShared)
        
        this.initializeExtended();
    }
    
    initializeExtended() {
        // Additional functionality on top of UsersComposable
        this.advancedFeatures = ref({
            bulkOperations: true,
            advancedFiltering: true,
            exportOptions: ['pdf', 'excel', 'csv']
        });
    }
    
    async bulkUpdateUsers(userIds, updateData) {
        // Use inherited validation and methods
        if (!this.valid.value) return false;
        
        try {
            this.isLoading.value = true;
            
            // Use inherited service
            const promises = userIds.map(id => 
                this.service.value.update(id, updateData)
            );
            
            await Promise.all(promises);
            
            // Use inherited methods
            await this.loadData();
            this.isLoading.value = false;
            
            notify('Bulk update completed successfully');
        } catch (error) {
            await this.errorHandle(error);
        }
    }
    
    async exportUsers(format = 'excel') {
        // Use inherited properties
        const data = this.tableData.value;
        
        // Custom export logic using inherited data
        console.log(`Exporting ${data.length} users to ${format}`);
    }
}

// ===== EXAMPLE 6: Benefits of Inheritance Approach =====

/**
 * Benefits of Inheritance Approach:
 * 
 * 1. **Direct Access**: All parent properties and methods are directly available
 * 2. **No Wrapper Functions**: No need for composable wrappers or getter methods
 * 3. **True OOP**: Follows object-oriented programming principles
 * 4. **Method Overriding**: Child classes can override parent methods
 * 5. **Polymorphism**: Different classes can implement same interface differently
 * 6. **Code Reuse**: Maximum code reuse through inheritance
 * 7. **Type Safety**: Better TypeScript support with inheritance
 * 8. **Performance**: No function call overhead or object spreading
 * 
 * Usage Patterns:
 * 
 * 1. **Single Instance**: Create one instance per component/page
 * 2. **Singleton Pattern**: Use singleton for global state management
 * 3. **Multiple Inheritance**: Extend existing classes for additional functionality
 * 4. **Custom Classes**: Create custom classes inheriting from BaseShared
 */

export {
    basicInheritanceExample,
    vueComponentInheritanceExample,
    CustomModuleManager,
    SingletonUsersManager,
    ExtendedUsersManager
};
