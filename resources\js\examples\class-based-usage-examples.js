/**
 * Examples of how to use the new class-based approach for shared utilities and composables
 * 
 * This file demonstrates the different ways to use the new class-based system
 * alongside the existing function-based composables for backward compatibility.
 */

// ===== EXAMPLE 1: Using the class-based shared utilities =====

// Old way (function-based)
import useShared from "@/helpers/shared.js";

const oldWay = () => {
    const {
        valid,
        isLoading,
        service,
        updateItem,
        router,
        userPermissions,
    } = useShared();
    
    // Use the properties and methods...
};

// New way (class-based with composable wrapper)
import useSharedClass from "@/helpers/useSharedClass.js";

const newWayComposable = () => {
    const {
        valid,
        isLoading,
        service,
        updateItem,
        router,
        userPermissions,
    } = useSharedClass();
    
    // Use the properties and methods...
};

// New way (direct class usage)
import { BaseShared } from "@/helpers/useSharedClass.js";

const newWayDirect = () => {
    const sharedInstance = new BaseShared();
    const {
        valid,
        isLoading,
        service,
        updateItem,
        router,
        userPermissions,
    } = sharedInstance.getAll();
    
    // Use the properties and methods...
};

// ===== EXAMPLE 2: Using the class-based users composable =====

// Old way (function-based)
import useUsers from "@/modules/users-management/composables/users.js";

const oldUsersWay = () => {
    const {
        getRoles,
        roles,
        itemData,
        getItem,
        validation,
    } = useUsers();
    
    // Use the properties and methods...
};

// New way (class-based with composable wrapper)
import useUsersClass from "@/modules/users-management/composables/useUsersClass.js";

const newUsersWayComposable = () => {
    const {
        getRoles,
        roles,
        itemData,
        getItem,
        validation,
    } = useUsersClass();
    
    // Use the properties and methods...
};

// New way (direct class usage)
import { UsersComposable } from "@/modules/users-management/composables/useUsersClass.js";

const newUsersWayDirect = () => {
    const usersInstance = new UsersComposable();
    const {
        getRoles,
        roles,
        itemData,
        getItem,
        validation,
    } = usersInstance.getAllUsers();
    
    // Use the properties and methods...
};

// ===== EXAMPLE 3: Using the class-based validations =====

// Old way (function-based)
import useValidations from '@/helpers/validations.js';
import {useI18n} from "vue-i18n";

const oldValidationsWay = () => {
    const {t} = useI18n({});
    const validationRules = useValidations(t);
    
    // Use validation rules...
};

// New way (class-based with composable wrapper)
import useValidationsClass from '@/helpers/useValidationsClass.js';

const newValidationsWayComposable = () => {
    const validationRules = useValidationsClass();
    
    // Use validation rules...
};

// New way (direct class usage)
import { BaseValidations } from '@/helpers/useValidationsClass.js';
import {useI18n} from "vue-i18n";

const newValidationsWayDirect = () => {
    const {t} = useI18n({});
    const validationsInstance = new BaseValidations(t);
    const validationRules = validationsInstance.getValidationRules();
    
    // Use validation rules...
    
    // Additional class methods available:
    validationsInstance.addCustomRule('customRule', (value) => value.length > 5 || 'Too short');
    const isValid = validationsInstance.validateValue('test', 'required');
    const result = validationsInstance.validateObject(
        { email: '<EMAIL>', password: '123' },
        {
            email: [validationRules.required, validationRules.email],
            password: [validationRules.required, validationRules.password]
        }
    );
};

// ===== EXAMPLE 4: Creating custom composables extending BaseShared =====

import BaseShared from "@/helpers/BaseShared.js";
import CustomService from "@/services/custom-service.js";

class CustomComposable extends BaseShared {
    constructor() {
        super();
        this.initializeCustom();
    }

    initializeCustom() {
        this.service.value = CustomService;
        this.customProperty = ref('custom value');
    }

    customMethod() {
        // Custom logic here
        console.log('Custom method called');
    }

    getCustomProperties() {
        return {
            ...this.getReactiveProperties(),
            customProperty: this.customProperty
        };
    }

    getCustomMethods() {
        return {
            ...this.getMethods(),
            customMethod: this.customMethod.bind(this)
        };
    }

    getAllCustom() {
        return {
            ...this.getCustomProperties(),
            ...this.getCustomMethods()
        };
    }
}

// Usage in Vue component
const useCustomComposable = () => {
    const instance = new CustomComposable();
    return instance.getAllCustom();
};

// ===== EXAMPLE 5: Benefits of the class-based approach =====

/**
 * Benefits:
 * 
 * 1. **Inheritance**: Classes can extend BaseShared and inherit all functionality
 * 2. **Encapsulation**: Related data and methods are grouped together
 * 3. **Reusability**: Base classes can be extended for specific use cases
 * 4. **Type Safety**: Better TypeScript support (if migrating to TypeScript)
 * 5. **Method Binding**: Methods are properly bound to the class instance
 * 6. **State Management**: Singleton pattern ensures consistent state across components
 * 7. **Extensibility**: Easy to add new methods and properties to base classes
 * 8. **Backward Compatibility**: Old composable pattern still works through wrapper functions
 * 
 * Migration Strategy:
 * 
 * 1. Keep existing function-based composables for backward compatibility
 * 2. Gradually migrate components to use class-based approach
 * 3. New features should use the class-based approach
 * 4. Eventually deprecate function-based composables when all components are migrated
 */

export {
    oldWay,
    newWayComposable,
    newWayDirect,
    oldUsersWay,
    newUsersWayComposable,
    newUsersWayDirect,
    oldValidationsWay,
    newValidationsWayComposable,
    newValidationsWayDirect,
    useCustomComposable,
    CustomComposable
};
