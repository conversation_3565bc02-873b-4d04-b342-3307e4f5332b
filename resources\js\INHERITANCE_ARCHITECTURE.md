# Inheritance-Based Architecture

This document describes the inheritance-based architecture where module classes inherit from BaseShared, providing direct access to all shared variables and functions through true object-oriented inheritance.

## Overview

The architecture uses class inheritance where module-specific classes extend BaseShared, inheriting all shared functionality directly. This eliminates the need for composable wrappers, getter methods, or state management complexity while providing true OOP benefits.

## Architecture Components

### 1. BaseShared (`resources/js/helpers/BaseShared.js`)
- **Base class** with all shared functionality
- **Singleton support** for global state management
- **Reactive properties** for Vue.js integration
- **CRUD operations** and error handling
- **Validation system** integration
- **Service management** capabilities

### 2. Inherited Module Classes
- **UsersComposable** extends BaseShared
- **AuthComposable** extends BaseShared
- **AddressesComposable** extends BaseShared
- **Custom classes** can extend BaseShared or other inherited classes

## Usage Pattern

### Basic Implementation

```javascript
// Import the class directly
import { UsersComposable } from "@/modules/users-management/composables/UsersComposable.js";

// Create instance - inherits ALL BaseShared functionality
const usersManager = new UsersComposable();

// Access inherited properties directly
console.log(usersManager.isLoading);      // BaseShared property
console.log(usersManager.valid);          // BaseShared property
console.log(usersManager.tableData);      // BaseShared property
console.log(usersManager.validationRules); // BaseShared property

// Access module-specific properties
console.log(usersManager.roles);          // UsersComposable property
console.log(usersManager.validation);     // UsersComposable property

// Use inherited methods directly
await usersManager.loadData();            // BaseShared method
await usersManager.getItem(1);            // Overridden in UsersComposable
await usersManager.updateItem(data);      // BaseShared method
await usersManager.deleteItem(1);         // BaseShared method

// Use module-specific methods
await usersManager.getRoles();            // UsersComposable method
```

### Vue Component Example

```javascript
<template>
    <v-container v-if="!usersManager.isLoading.value">
        <v-form v-model="usersManager.valid" @submit.prevent="handleSubmit">
            <v-text-field
                v-model="usersManager.itemData.value.first_name"
                :label="usersManager.t('users.first_name')"
                :rules="usersManager.validation.first_name"
            />
            <v-select
                v-model="usersManager.itemData.value.roles"
                :items="usersManager.roles.value"
                :rules="usersManager.validation.roles"
                multiple
            />
            <v-btn type="submit" :disabled="!usersManager.valid.value">
                {{ usersManager.t('save') }}
            </v-btn>
        </v-form>
    </v-container>
</template>

<script setup>
import { onMounted } from 'vue';
import { UsersComposable } from "@/modules/users-management/composables/UsersComposable.js";

const props = defineProps({
    id: { required: true, type: String }
});

// Direct class instantiation with inheritance
const usersManager = new UsersComposable();

onMounted(async () => {
    // All methods available directly
    await usersManager.getRoles(true);
    await usersManager.getItem(props.id, true);
});

const handleSubmit = async () => {
    await usersManager.updateItem(usersManager.itemData.value, 'users', true);
};
</script>
```

## Inheritance Hierarchy

```
BaseShared
├── UsersComposable
│   └── ExtendedUsersManager (custom extension)
├── AuthComposable
├── AddressesComposable
└── CustomModuleManager (custom class)
```

## Creating Custom Classes

### Extending BaseShared

```javascript
import BaseShared from "@/helpers/BaseShared.js";

export class CustomModuleManager extends BaseShared {
    constructor() {
        super(); // Inherit all BaseShared functionality
        
        this.initializeCustom();
        this.setupCustomValidation();
    }
    
    initializeCustom() {
        // Set service (inherited property)
        this.service.value = CustomService;
        
        // Custom properties
        this.customData = ref([]);
        this.customSettings = reactive({
            theme: 'dark',
            language: 'en'
        });
    }
    
    setupCustomValidation() {
        // Use inherited validationRules
        this.customValidation = {
            customField: [
                this.validationRules.required,
                this.validationRules.minLength(3)
            ]
        };
    }
    
    async loadCustomData() {
        try {
            // Use inherited properties and methods
            this.isLoading.value = true;
            const response = await this.service.value.index({});
            this.customData.value = response.data.data;
            this.isLoading.value = false;
        } catch (error) {
            // Use inherited error handling
            await this.errorHandle(error);
        }
    }
}
```

### Extending Existing Module Classes

```javascript
import { UsersComposable } from "@/modules/users-management/composables/UsersComposable.js";

export class ExtendedUsersManager extends UsersComposable {
    constructor() {
        super(); // Inherit from UsersComposable (which inherits from BaseShared)
        
        this.initializeExtended();
    }
    
    initializeExtended() {
        // Additional functionality
        this.advancedFeatures = ref({
            bulkOperations: true,
            advancedFiltering: true
        });
    }
    
    async bulkUpdateUsers(userIds, updateData) {
        // Use inherited validation and methods
        if (!this.valid.value) return false;
        
        try {
            this.isLoading.value = true;
            
            const promises = userIds.map(id => 
                this.service.value.update(id, updateData)
            );
            
            await Promise.all(promises);
            await this.loadData(); // Inherited method
            
            this.isLoading.value = false;
        } catch (error) {
            await this.errorHandle(error); // Inherited method
        }
    }
}
```

## Singleton Pattern Support

```javascript
export class SingletonUsersManager extends BaseShared {
    static instance = null;
    
    constructor() {
        if (SingletonUsersManager.instance) {
            return SingletonUsersManager.instance;
        }
        
        super();
        this.initializeUsers();
        SingletonUsersManager.instance = this;
    }
    
    static getInstance() {
        if (!SingletonUsersManager.instance) {
            SingletonUsersManager.instance = new SingletonUsersManager();
        }
        return SingletonUsersManager.instance;
    }
}

// Usage
const manager1 = SingletonUsersManager.getInstance();
const manager2 = SingletonUsersManager.getInstance();
console.log(manager1 === manager2); // true - same instance
```

## Benefits

### 1. True Object-Oriented Programming
- **Inheritance**: Child classes inherit all parent functionality
- **Polymorphism**: Different classes can implement same interface differently
- **Encapsulation**: Related data and methods grouped in classes
- **Method Overriding**: Child classes can override parent methods

### 2. Performance
- **No Function Overhead**: Direct property/method access
- **No Object Spreading**: No need to spread objects or use getter methods
- **Memory Efficiency**: Single instance with inherited properties
- **Faster Access**: Direct property access instead of function calls

### 3. Developer Experience
- **Direct Access**: All properties and methods available directly
- **No Wrappers**: No need for composable wrappers or getter functions
- **Clear Hierarchy**: Easy to understand inheritance relationships
- **Type Safety**: Better TypeScript support with inheritance

### 4. Code Organization
- **Maximum Reuse**: Inherit all functionality without duplication
- **Clear Structure**: Inheritance hierarchy shows relationships
- **Easy Extension**: Simple to add new functionality through inheritance
- **Maintainable**: Changes to base class affect all children

## Migration from Composables

### Before (Composable Pattern)
```javascript
import useShared from "@/helpers/shared.js";
import useUsers from "../composables/users.js";

const {
    valid,
    isLoading,
    updateItem
} = useShared();

const {
    getRoles,
    roles,
    getItem
} = useUsers();

// Use methods
await getRoles();
await getItem(1);
await updateItem(data);
```

### After (Inheritance Pattern)
```javascript
import { UsersComposable } from "../composables/UsersComposable.js";

const usersManager = new UsersComposable();

// All properties and methods available directly
console.log(usersManager.valid);
console.log(usersManager.isLoading);
console.log(usersManager.roles);

// Use methods directly
await usersManager.getRoles();
await usersManager.getItem(1);
await usersManager.updateItem(data);
```

## Best Practices

1. **Use Inheritance**: Extend BaseShared for new module classes
2. **Call super()**: Always call parent constructor in child classes
3. **Override When Needed**: Override parent methods for specific behavior
4. **Use Singleton**: Consider singleton pattern for global state
5. **Direct Access**: Access properties and methods directly from instance
6. **Proper Initialization**: Initialize module-specific properties in constructor
7. **Error Handling**: Use inherited error handling methods
8. **Service Management**: Set appropriate services in child classes
