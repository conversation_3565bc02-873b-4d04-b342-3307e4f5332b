import AddressesComposable from './AddressesComposable.js';

// Create a singleton instance to maintain state across components
let addressesInstance = null;

export default function useAddressesClass() {
    if (!addressesInstance) {
        addressesInstance = new AddressesComposable();
    }
    
    return addressesInstance.getAllAddresses();
}

// Export the class for direct usage
export { AddressesComposable };
