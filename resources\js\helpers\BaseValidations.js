import * as validators from '@vuelidate/validators';

export default class BaseValidations {
    constructor(t) {
        this.t = t;
        this.initializeValidationRules();
    }

    initializeValidationRules() {
        this.validationRules = {
            email: value => 
                validators.email.$validator(value) || this.t('validation.email'),
            
            mobile: value => 
                /^09[0-9]{8}$/.test(value) || this.t('validation.mobile'),
            
            phone: value => 
                /^0[0-9]{9}$/.test(value) || this.t('validation.phone'),
            
            password: value =>
                (!validators.helpers.req(value) || /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{8,20}$/.test(value)) || this.t('validation.password'),
            
            required: value => 
                validators.required.$validator(value) || this.t('validation.required'),
            
            optional: value => 
                /^(?!\s)(?!\s+$).*/.test(value) || this.t('validation.optional'),
            
            minLength: length => value =>
              validators.minLength(length).$validator(value) || this.t('validation.minLength', { length }),
            
            maxLength: length => value =>
              validators.maxLength(length).$validator(value) || this.t('validation.maxLength', { length }),
            
            minValue: min => value =>
              validators.minValue(min).$validator(value) || this.t('validation.minValue', { min }),
            
            maxValue: max => value =>
              validators.maxValue(max).$validator(value) || this.t('validation.maxValue', { max }),
            
            alpha: value => 
                validators.alpha.$validator(value) || this.t('validation.alpha'),
            
            alphaNum: value => 
                validators.alphaNum.$validator(value) || this.t('validation.alphaNum'),
            
            numeric: value => 
                validators.numeric.$validator(value) || this.t('validation.numeric'),
            
            integer: value => 
                validators.integer.$validator(value) || this.t('validation.integer'),
            
            decimal: value => 
                validators.decimal.$validator(value) || this.t('validation.decimal'),
            
            between: (min, max) => value =>
              validators.between(min, max).$validator(value) || this.t('validation.between', { min, max }),
            
            url: value => 
                validators.url.$validator(value) || this.t('validation.url'),
            
            sameAs: comparedValue => value =>
              validators.sameAs(comparedValue).$validator(value) || this.t('validation.sameAs', {comparedValue}),

            requiredIf: (condition, message = '') => value => 
                validators.requiredIf(condition).$validator(value) || this.t('validation.requiredIf', { message }),

            requiredUnless: (condition, message = '') => value => 
                validators.requiredUnless(condition).$validator(value) || this.t('validation.requiredUnless', { message }),

            contains: (param) => (value) =>
                (!validators.helpers.req(value) || value.includes(param)) || this.t('validation.contains',{ string: param }),

            startsWith: string => value => 
                (!validators.helpers.req(value) || value.startsWith(string)) || this.t('validation.startsWith',{ string }),

            endsWith: string => value => 
                (!validators.helpers.req(value) || value.endsWith(string)) || this.t('validation.endsWith',{ string }),

            beforeOrEqual: toDate => value => 
                (new Date(toDate) >= new Date(value)) || this.t('validation.beforeOrEqual', { toDate }),

            before: toDate => value => 
                (new Date(toDate) > new Date(value)) || this.t('validation.before', { toDate }),

            afterOrEqual: fromDate => value => 
                (new Date(fromDate) <= new Date(value)) || this.t('validation.afterOrEqual', { fromDate }),

            after: fromDate => value => 
                (new Date(fromDate) < new Date(value)) || this.t('validation.after', { fromDate })
        };
    }

    getValidationRules() {
        return this.validationRules;
    }

    // Method to add custom validation rules
    addCustomRule(name, rule) {
        this.validationRules[name] = rule;
    }

    // Method to get a specific validation rule
    getRule(name) {
        return this.validationRules[name];
    }

    // Method to validate a single value against a rule
    validateValue(value, ruleName, ...params) {
        const rule = this.validationRules[ruleName];
        if (!rule) {
            throw new Error(`Validation rule '${ruleName}' not found`);
        }
        
        if (typeof rule === 'function') {
            if (params.length > 0) {
                return rule(...params)(value);
            }
            return rule(value);
        }
        
        return true;
    }

    // Method to validate an object against multiple rules
    validateObject(obj, validationSchema) {
        const errors = {};
        
        for (const [field, rules] of Object.entries(validationSchema)) {
            const value = obj[field];
            const fieldErrors = [];
            
            for (const rule of rules) {
                const result = rule(value);
                if (result !== true) {
                    fieldErrors.push(result);
                }
            }
            
            if (fieldErrors.length > 0) {
                errors[field] = fieldErrors;
            }
        }
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
}
