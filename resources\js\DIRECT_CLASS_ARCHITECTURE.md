# Direct Class Architecture with Global State Management

This document describes the direct class-based architecture with global state management, eliminating the need for composable wrappers and providing a more efficient, centralized state management system.

## Overview

The architecture uses direct class instantiation with a global state manager (SharedStateManager) that acts as a singleton to maintain consistent state across the entire application. Module-specific classes work with this global state rather than maintaining their own isolated state.

## Architecture Components

### 1. SharedStateManager (`resources/js/helpers/SharedStateManager.js`)
- **Singleton Pattern**: Ensures single instance across the application
- **Global State**: Centralized reactive state management
- **Service Management**: Handles service registration and switching
- **CRUD Operations**: Common operations available globally
- **Error Handling**: Centralized error management
- **State Subscription**: Allows components to subscribe to state changes

### 2. Module-Specific Classes
- **UsersComposable**: User management functionality
- **AuthComposable**: Authentication functionality  
- **AddressesComposable**: Address management functionality
- **Custom Classes**: Extensible for new modules

## Usage Pattern

### Basic Implementation

```javascript
// Import classes directly
import { SharedStateManager } from "@/helpers/SharedStateManager.js";
import { UsersComposable } from "@/modules/users-management/composables/useUsersClass.js";

// Get global state manager (singleton)
const sharedState = SharedStateManager.getInstance();

// Create module manager
const usersManager = new UsersComposable();

// Access global state
const {
    valid,
    isLoading,
    itemData,
    updateItem,
    router,
    t
} = sharedState.getState();

// Access module-specific functionality
const {
    roles,
    getRoles,
    getItem,
    validation
} = usersManager.getAllUsers();
```

### Vue Component Example

```javascript
<script setup>
import { onMounted } from 'vue';
import { SharedStateManager } from "@/helpers/SharedStateManager.js";
import { UsersComposable } from "@/modules/users-management/composables/useUsersClass.js";

const props = defineProps({
    id: { required: true, type: String }
});

// Direct class instantiation
const sharedState = SharedStateManager.getInstance();
const usersManager = new UsersComposable();

// Access state and methods directly
const {
    valid,
    isLoading,
    itemData,
    updateItem,
    t
} = sharedState.getState();

const {
    getRoles,
    getItem,
    validation
} = usersManager.getAllUsers();

onMounted(async () => {
    await getRoles(true);
    await getItem(props.id, true);
});

const handleSubmit = async () => {
    await updateItem(itemData.value, 'users', true);
};
</script>
```

## Global State Management Features

### State Access
```javascript
const sharedState = SharedStateManager.getInstance();

// Get all state and methods
const state = sharedState.getState();

// Update specific state values
sharedState.updateState('isLoading', false);

// Reset state
sharedState.resetState();
```

### Service Management
```javascript
// Set services globally
sharedState.setService(UsersService);
sharedState.setInnerService(RolesService);
sharedState.setDetailsService(DetailsService);
```

### State Subscription
```javascript
// Subscribe to state changes
sharedState.subscribe((newState, oldState) => {
    console.log('State changed:', { newState, oldState });
});
```

### Debug Features
```javascript
// Log current state
sharedState.logState();

// Get state snapshot
const snapshot = sharedState.getStateSnapshot();
```

## Module-Specific Classes

### UsersComposable
```javascript
const usersManager = new UsersComposable();

// Automatically sets UsersService in global state
// Provides user-specific methods and validation

const {
    roles,           // User roles data
    getRoles,        // Fetch roles method
    getItem,         // Get user item (overrides base)
    validation       // User validation rules
} = usersManager.getAllUsers();
```

### AuthComposable
```javascript
const authManager = new AuthComposable();

const {
    userCredentials, // Login form data
    login,           // Login method
    validation,      // Auth validation rules
    showPassword,    // Password visibility toggle
    valid,           // Form validity
    form             // Form reference
} = authManager.getAllAuth();
```

### AddressesComposable
```javascript
const addressesManager = new AddressesComposable();

// Automatically sets AddressesService in global state
// Provides address-specific functionality

const {
    form,            // Address form data
    validation,      // Address validation rules
    addressCols,     // Table columns
    addressActions   // Table actions
} = addressesManager.getAllAddresses();
```

## Creating Custom Module Classes

```javascript
import { SharedStateManager } from "@/helpers/SharedStateManager.js";

export class CustomModuleManager {
    constructor() {
        this.sharedState = SharedStateManager.getInstance();
        this.initializeCustom();
    }
    
    initializeCustom() {
        // Set custom service
        this.sharedState.setService(CustomService);
        
        // Custom properties
        this.customData = ref([]);
        this.customSettings = reactive({
            theme: 'dark',
            language: 'en'
        });
    }
    
    async loadCustomData() {
        const { service, errorHandle } = this.sharedState.getState();
        try {
            const response = await service.value.index({});
            this.customData.value = response.data.data;
        } catch (error) {
            await errorHandle(error);
        }
    }
    
    getCustomState() {
        return {
            ...this.sharedState.getState(),
            customData: this.customData,
            customSettings: this.customSettings,
            loadCustomData: this.loadCustomData.bind(this)
        };
    }
}
```

## Benefits

### 1. Performance
- **No Composable Overhead**: Direct method calls without function wrappers
- **Memory Efficiency**: Singleton pattern reduces memory usage
- **Faster State Access**: Direct property access instead of reactive unwrapping

### 2. State Management
- **Global Consistency**: Single source of truth across all components
- **State Persistence**: State persists across component lifecycle
- **Centralized Updates**: All state changes go through the global manager

### 3. Developer Experience
- **Explicit Dependencies**: Clear understanding of what each component uses
- **Better Debugging**: Centralized state makes debugging easier
- **Type Safety**: Better TypeScript support with direct class usage

### 4. Architecture
- **Scalability**: Easy to add new modules and functionality
- **Maintainability**: Clear separation of concerns
- **Testability**: Direct class instantiation makes unit testing simpler

## Migration from Composables

### Before (Composable Pattern)
```javascript
import useShared from "@/helpers/shared.js";
import useUsers from "../composables/users.js";

const {
    valid,
    isLoading,
    updateItem
} = useShared();

const {
    getRoles,
    roles,
    getItem
} = useUsers();
```

### After (Direct Class Pattern)
```javascript
import { SharedStateManager } from "@/helpers/SharedStateManager.js";
import { UsersComposable } from "../composables/useUsersClass.js";

const sharedState = SharedStateManager.getInstance();
const usersManager = new UsersComposable();

const {
    valid,
    isLoading,
    updateItem
} = sharedState.getState();

const {
    getRoles,
    roles,
    getItem
} = usersManager.getAllUsers();
```

## Best Practices

1. **Use Singleton Pattern**: Always get SharedStateManager instance via `getInstance()`
2. **Initialize Once**: Create module managers once per component/page
3. **Destructure Wisely**: Only destructure the properties and methods you need
4. **Handle Errors**: Use the global error handler for consistency
5. **Subscribe to Changes**: Use state subscription for reactive updates
6. **Debug Effectively**: Use built-in debug methods for troubleshooting
7. **Service Management**: Set appropriate services for each module
8. **State Cleanup**: Reset state when navigating between different modules
