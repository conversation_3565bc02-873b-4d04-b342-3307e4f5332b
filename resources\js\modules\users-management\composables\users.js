import {reactive, ref} from 'vue'
import UsersService from "@/services/users-service.js";
import useShared from "@/helpers/shared.js";
import RolesService from "@/services/roles-service.js";
import userTableItems from "../models/user-table-items";
import roleTableItems from "../models/role-table-items";

export default function useUsers() {
    const {
        validationRules,
        
        isLoading,
        service,
        itemData,
        parent,
        errorHandle,
        t
        } = useShared()

        const roles = ref([])

        service.value = UsersService;

        const validation = {
        first_name: [
            validationRules.required,
        ],
        last_name: [
            validationRules.required,
        ],
        email: [
            validationRules.required,
            validationRules.email
        ],
        password: [
            validationRules.required
            ,validationRules.password
        ],
        roles: [
            validationRules.required,
        ],
    }


    const getItem = async (id, showLoader = false) => {
        try {
            const response = await service.value.show(id, showLoader);
            itemData.value = response.data.data['user'];
            itemData.value['roles'] = response.data.data['roles'];
            isLoading.value = false
        } catch (error) {
            await errorHandle(error)
        }
    }


    
    const getRoles = async (showLoader = false) => {
        try {
            const {data: {data}} = await RolesService.index({
                parent_id: '',
                page: 1,
                size: 1000000,
                search: '',
            }, showLoader);
            roles.value = data;
        } catch (error) {
            errorHandle(error)
        }
    }

    

    return {
        roles,
        getItem,
        getRoles,
        validation,
        itemData,
        parent,
        t
    }
}